{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2>{{ title }}</h2>
    <div class="card">
        <div class="card-header">
            Admission Details
        </div>
        <div class="card-body">
            <p><strong>Patient:</strong> {{ admission.patient.get_full_name }} (ID: {{ admission.patient.patient_id }})</p>
            <p><strong>Admission Date:</strong> {{ admission.admission_date }}</p>
            <p><strong>Ward:</strong> {{ admission.bed.ward.name }}</p>
            <p><strong>Bed:</strong> {{ admission.bed.bed_number }}</p>
            <p><strong>Attending Doctor:</strong> {{ admission.attending_doctor.get_full_name }}</p>
            <p><strong>Diagnosis:</strong> {{ admission.diagnosis }}</p>
            <p><strong>Days Spent:</strong> {{ admission.get_duration }}</p>
            <p><strong>Admission Charges:</strong> ₦{{ admission.get_total_cost|floatformat:2 }}</p>
            <p><strong>Patient Wallet Balance:</strong> ₦{{ admission.patient.wallet.balance|floatformat:2 }}</p>
        </div>
        <div class="card-footer">
            <a href="{% url 'inpatient:edit_admission' admission.id %}" class="btn btn-primary">Edit Admission</a>
            <a href="{% url 'inpatient:discharge_patient' admission.id %}" class="btn btn-danger">Discharge Patient</a>
            <a href="{% url 'inpatient:transfer_patient' admission.id %}" class="btn btn-warning">Transfer Patient</a>
            <a href="{% url 'patients:wallet_dashboard' admission.patient.id %}" class="btn btn-info">View Wallet</a>
        </div>
    </div>

    <div class="mt-4">
        {% comment %} <h4>Daily Rounds</h4> {% endcomment %}
        {% comment %} <h4 class="d-inline-block ms-4">Nursing Notes</h4> {% endcomment %}
        <div class="d-flex gap-2 mb-3">
            <!-- Button to trigger Daily Round Modal -->
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#dailyRoundModal">
                Add Daily Round
            </button>

            <!-- Daily Round Modal -->
            <div class="modal fade" id="dailyRoundModal" tabindex="-1" aria-labelledby="dailyRoundModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="dailyRoundModalLabel">Add Daily Round</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form method="post">
                            {% csrf_token %}
                            <div class="modal-body">
                                {{ round_form.as_p }}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <input type="hidden" name="add_round" value="true">
                                <button type="submit" class="btn btn-success">Save Daily Round</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#nursingNoteModal">
                Add Nursing Note
            </button>

            <!-- Nursing Note Modal -->
            <div class="modal fade" id="nursingNoteModal" tabindex="-1" aria-labelledby="nursingNoteModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="nursingNoteModalLabel">Add Nursing Note</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form method="post">
                            {% csrf_token %}
                            <div class="modal-body">
                                {{ note_form.as_p }}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <input type="hidden" name="add_note" value="true">
                                <button type="submit" class="btn btn-success">Save Nursing Note</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% for round in daily_rounds %}
            <div class="card mb-2">
                <div class="card-body">
                    <p><strong>Doctor:</strong> {{ round.doctor.get_full_name }}</p>
                    <p><strong>Date:</strong> {{ round.date_time }}</p>
                    <p><strong>Notes:</strong> {{ round.notes }}</p>
                </div>
            </div>
        {% endfor %}
        {% for note in nursing_notes %}
            <div class="card mb-2">
                <div class="card-body">
                    <p><strong>Nurse:</strong> {{ note.nurse.get_full_name }}</p>
                    <p><strong>Date:</strong> {{ note.date_time }}</p>
                    <p><strong>Notes:</strong> {{ note.notes }}</p>
                </div>
            </div>
        {% endfor %}
    </div>

</div>
{% endblock %}