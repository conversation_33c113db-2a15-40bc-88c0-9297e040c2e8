{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .dispensing-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .prescription-header {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid #007bff;
    }
    
    .dispensary-selection {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .items-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .table th {
        background: #007bff;
        color: white;
        border: none;
        font-weight: 600;
    }
    
    .stock-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .stock-available {
        background: #d4edda;
        color: #155724;
    }
    
    .stock-low {
        background: #fff3cd;
        color: #856404;
    }
    
    .stock-out {
        background: #f8d7da;
        color: #721c24;
    }
    
    .stock-loading {
        background: #e2e3e5;
        color: #6c757d;
    }
    
    .quantity-input {
        width: 80px;
    }
    
    .btn-dispense {
        background: #28a745;
        border-color: #28a745;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .btn-dispense:hover {
        background: #218838;
        border-color: #1e7e34;
    }
    
    .quick-actions {
        margin-bottom: 15px;
    }
    
    .alert {
        border-radius: 8px;
    }
    
    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="dispensing-container">
    <!-- Prescription Header -->
    <div class="prescription-header">
        <div class="row">
            <div class="col-md-6">
                <h4 class="mb-2">{{ title }}</h4>
                <p class="mb-1"><strong>Patient:</strong> {{ prescription.patient.get_full_name }}</p>
                <p class="mb-1"><strong>Doctor:</strong> {{ prescription.doctor.get_full_name }}</p>
                <p class="mb-0"><strong>Date:</strong> {{ prescription.prescription_date|date:"M d, Y" }}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <span class="badge badge-{{ prescription.status|default:'secondary' }} badge-lg">{{ prescription.get_status_display }}</span>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Dispensary Selection -->
    <div class="dispensary-selection">
        <h5 class="mb-3">Select Dispensary</h5>
        <div class="row align-items-center">
            <div class="col-md-6">
                <select id="dispensary-select" class="form-select" required>
                    <option value="">Choose dispensary...</option>
                    {% for dispensary in dispensaries %}
                        <option value="{{ dispensary.id }}">{{ dispensary.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <div id="dispensary-status" class="text-muted">
                    <small>Please select a dispensary to view stock levels</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Prescription Items -->
    <div class="items-table">
        <div class="table-responsive">
            <form id="dispense-form" method="post">
                {% csrf_token %}
                <input type="hidden" id="dispensary-id-input" name="dispensary_id" value="">
                
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="5%">
                                <input type="checkbox" id="select-all" class="form-check-input">
                            </th>
                            <th>Medication</th>
                            <th>Prescribed Qty</th>
                            <th>Dispensed</th>
                            <th>Remaining</th>
                            <th>Stock Status</th>
                            <th>Dispense Qty</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in prescription_items %}
                            <tr data-item-id="{{ item.id }}">
                                <td>
                                    <input type="checkbox" 
                                           name="dispense_item_{{ item.id }}" 
                                           class="form-check-input item-checkbox" 
                                           disabled>
                                </td>
                                <td>
                                    <strong>{{ item.medication.name }}</strong>
                                    <br><small class="text-muted">{{ item.medication.generic_name }}</small>
                                </td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.quantity_dispensed_so_far }}</td>
                                <td>{{ item.remaining_quantity_to_dispense }}</td>
                                <td>
                                    <span class="stock-status stock-loading" data-stock-cell>
                                        <span class="loading-spinner"></span> Loading...
                                    </span>
                                </td>
                                <td>
                                    <input type="number" 
                                           name="quantity_{{ item.id }}" 
                                           class="form-control quantity-input" 
                                           min="1" 
                                           max="{{ item.remaining_quantity_to_dispense }}"
                                           value="{{ item.remaining_quantity_to_dispense }}"
                                           disabled>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        No items available for dispensing
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                {% if prescription_items %}
                    <div class="p-3 border-top">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="quick-actions">
                                    <button type="button" id="select-all-btn" class="btn btn-outline-primary btn-sm" disabled>
                                        Select All
                                    </button>
                                    <button type="button" id="clear-all-btn" class="btn btn-outline-secondary btn-sm" disabled>
                                        Clear All
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <button type="submit" id="dispense-btn" class="btn btn-dispense" disabled>
                                    <i class="fas fa-pills"></i> Dispense Selected Items
                                </button>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dispensarySelect = document.getElementById('dispensary-select');
    const dispensaryIdInput = document.getElementById('dispensary-id-input');
    const dispensaryStatus = document.getElementById('dispensary-status');
    const selectAllCheckbox = document.getElementById('select-all');
    const selectAllBtn = document.getElementById('select-all-btn');
    const clearAllBtn = document.getElementById('clear-all-btn');
    const dispenseBtn = document.getElementById('dispense-btn');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const quantityInputs = document.querySelectorAll('.quantity-input');
    
    // Handle dispensary selection
    dispensarySelect.addEventListener('change', function() {
        const dispensaryId = this.value;
        dispensaryIdInput.value = dispensaryId;

        if (dispensaryId) {
            console.log('Dispensary selected:', dispensaryId);

            // Immediately show items as loading
            showItemsAsLoading();

            // Then fetch actual stock quantities
            fetchStockQuantities(dispensaryId);
        } else {
            resetStockDisplay();
            disableAllControls();
        }
    });

    // Show items as loading immediately when dispensary is selected
    function showItemsAsLoading() {
        console.log('Showing items as loading...');
        const stockCells = document.querySelectorAll('[data-stock-cell]');

        stockCells.forEach((cell) => {
            cell.className = 'stock-status stock-loading';
            cell.innerHTML = '<span class="loading-spinner"></span> Loading...';
        });

        // Enable basic controls
        enableGlobalControls();
    }
    
    // Fetch stock quantities via AJAX
    function fetchStockQuantities(dispensaryId) {
        console.log('Fetching stock quantities for dispensary:', dispensaryId);
        dispensaryStatus.innerHTML = '<span class="loading-spinner"></span> Loading stock information...';

        // Use the correct URL with the prescription ID
        const url = `/pharmacy/prescriptions/{{ prescription.id }}/stock-quantities/`;
        console.log('AJAX URL:', url);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `dispensary_id=${dispensaryId}`
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                updateStockDisplay(data.stock_quantities);
                dispensaryStatus.innerHTML = '<i class="fas fa-check-circle text-success"></i> Stock information loaded';
            } else {
                throw new Error(data.error || 'Failed to load stock information');
            }
        })
        .catch(error => {
            console.error('Error fetching stock:', error);
            dispensaryStatus.innerHTML = '<i class="fas fa-exclamation-triangle text-danger"></i> Error loading stock information: ' + error.message;
            resetStockDisplay();

            // Show items even if stock loading failed
            enableItemsWithoutStock();
        });
    }
    
    // Update stock display
    function updateStockDisplay(stockData) {
        console.log('Updating stock display with data:', stockData);
        const stockCells = document.querySelectorAll('[data-stock-cell]');
        console.log('Found stock cells:', stockCells.length);

        stockCells.forEach((cell, index) => {
            const row = cell.closest('tr');
            const itemId = row.dataset.itemId;
            const checkbox = row.querySelector('.item-checkbox');
            const quantityInput = row.querySelector('.quantity-input');
            const stock = stockData[itemId] || 0;

            console.log(`Item ${itemId}: stock = ${stock}`);

            // Update stock display
            if (stock > 10) {
                cell.className = 'stock-status stock-available';
                cell.innerHTML = `<i class="fas fa-check-circle"></i> ${stock} available`;
            } else if (stock > 0) {
                cell.className = 'stock-status stock-low';
                cell.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${stock} available (Low)`;
            } else {
                cell.className = 'stock-status stock-out';
                cell.innerHTML = `<i class="fas fa-times-circle"></i> Out of stock`;
            }

            // Enable/disable controls based on stock
            if (stock > 0) {
                checkbox.disabled = false;
                quantityInput.disabled = false;
                quantityInput.max = Math.min(stock, parseInt(quantityInput.max));
                quantityInput.value = Math.min(parseInt(quantityInput.value), stock);
                console.log(`Enabled controls for item ${itemId}`);
            } else {
                checkbox.disabled = true;
                checkbox.checked = false;
                quantityInput.disabled = true;
                quantityInput.value = 0;
                console.log(`Disabled controls for item ${itemId} (no stock)`);
            }
        });

        enableGlobalControls();
        updateDispenseButton();
        console.log('Stock display update completed');
    }
    
    // Reset stock display
    function resetStockDisplay() {
        const stockCells = document.querySelectorAll('[data-stock-cell]');
        stockCells.forEach(cell => {
            cell.className = 'stock-status stock-loading';
            cell.innerHTML = '<span class="loading-spinner"></span> Select dispensary';
        });
    }
    
    // Disable all controls
    function disableAllControls() {
        itemCheckboxes.forEach(cb => {
            cb.disabled = true;
            cb.checked = false;
        });
        quantityInputs.forEach(input => input.disabled = true);
        selectAllCheckbox.disabled = true;
        selectAllBtn.disabled = true;
        clearAllBtn.disabled = true;
        dispenseBtn.disabled = true;
    }
    
    // Enable global controls
    function enableGlobalControls() {
        selectAllCheckbox.disabled = false;
        selectAllBtn.disabled = false;
        clearAllBtn.disabled = false;
    }

    // Enable items without stock information (for debugging)
    function enableItemsWithoutStock() {
        console.log('Enabling items without stock information');
        const stockCells = document.querySelectorAll('[data-stock-cell]');

        stockCells.forEach((cell) => {
            const row = cell.closest('tr');
            const checkbox = row.querySelector('.item-checkbox');
            const quantityInput = row.querySelector('.quantity-input');

            // Show as unknown stock but enable controls
            cell.className = 'stock-status stock-loading';
            cell.innerHTML = '<i class="fas fa-question-circle"></i> Stock unknown - proceed with caution';

            // Enable controls
            checkbox.disabled = false;
            quantityInput.disabled = false;
        });

        enableGlobalControls();
        updateDispenseButton();
    }
    
    // Handle select all
    selectAllCheckbox.addEventListener('change', function() {
        const availableCheckboxes = Array.from(itemCheckboxes).filter(cb => !cb.disabled);
        availableCheckboxes.forEach(cb => cb.checked = this.checked);
        updateDispenseButton();
    });
    
    selectAllBtn.addEventListener('click', function() {
        const availableCheckboxes = Array.from(itemCheckboxes).filter(cb => !cb.disabled);
        availableCheckboxes.forEach(cb => cb.checked = true);
        selectAllCheckbox.checked = true;
        updateDispenseButton();
    });
    
    // Handle clear all
    clearAllBtn.addEventListener('click', function() {
        itemCheckboxes.forEach(cb => cb.checked = false);
        selectAllCheckbox.checked = false;
        updateDispenseButton();
    });
    
    // Handle individual checkbox changes
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateDispenseButton);
    });
    
    // Update dispense button state
    function updateDispenseButton() {
        const checkedBoxes = Array.from(itemCheckboxes).filter(cb => cb.checked);
        dispenseBtn.disabled = checkedBoxes.length === 0 || !dispensarySelect.value;
        
        // Update select all checkbox state
        const availableCheckboxes = Array.from(itemCheckboxes).filter(cb => !cb.disabled);
        const checkedAvailable = availableCheckboxes.filter(cb => cb.checked);
        selectAllCheckbox.checked = availableCheckboxes.length > 0 && checkedAvailable.length === availableCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedAvailable.length > 0 && checkedAvailable.length < availableCheckboxes.length;
    }
    
    // Form submission validation
    document.getElementById('dispense-form').addEventListener('submit', function(e) {
        const checkedItems = Array.from(itemCheckboxes).filter(cb => cb.checked);
        
        if (checkedItems.length === 0) {
            e.preventDefault();
            alert('Please select at least one item to dispense.');
            return;
        }
        
        if (!dispensarySelect.value) {
            e.preventDefault();
            alert('Please select a dispensary.');
            return;
        }
        
        // Validate quantities
        let hasInvalidQuantity = false;
        checkedItems.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const quantityInput = row.querySelector('.quantity-input');
            const quantity = parseInt(quantityInput.value);
            
            if (!quantity || quantity <= 0) {
                hasInvalidQuantity = true;
            }
        });
        
        if (hasInvalidQuantity) {
            e.preventDefault();
            alert('Please enter valid quantities for all selected items.');
            return;
        }
        
        // Confirm dispensing
        if (!confirm(`Are you sure you want to dispense ${checkedItems.length} item(s)?`)) {
            e.preventDefault();
        }
    });
    
    // Initialize
    disableAllControls();
});
</script>
{% endblock %}