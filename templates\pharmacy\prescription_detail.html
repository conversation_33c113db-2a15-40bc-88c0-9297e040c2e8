{% extends 'base.html' %}
{% load form_tags %}
{% load pharmacy_tags %}

{% block title %}Prescription Details - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Prescription Details</h4>
                <div>
                    <a href="{% url 'pharmacy:print_prescription' prescription.id %}" class="btn btn-info me-2" target="_blank">
                        <i class="fas fa-print"></i> Print Prescription
                    </a>
                    <a href="{% url 'pharmacy:prescription_dispensing_history' prescription.id %}" class="btn btn-warning btn-sm me-2">
                        <i class="fas fa-history"></i> Dispensing History
                    </a>
                    {% if prescription.status != 'completed' and prescription.status != 'cancelled' %}
                        <a href="{% url 'pharmacy:dispense_prescription' prescription.id %}" class="btn btn-success me-2">
                            <i class="fas fa-prescription-bottle-alt"></i> Dispense Medications
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <!-- Info: Invoice is generated after dispensing -->
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> The invoice for dispensed medicines will be generated and sent to billing/Context7 MCP only after dispensing is confirmed by the pharmacist.
                </div>
                <!-- Billing Status and Quick Link -->
                <div class="row mb-3">
                    <div class="col-md-12 text-end">
                        {% if latest_invoice %}
                            <a href="{% url 'billing:detail' latest_invoice.id %}" class="btn btn-outline-primary">
                                <i class="fas fa-file-invoice"></i> View Latest Invoice
                            </a>
                            <span class="badge bg-{% if latest_invoice.status == 'paid' %}success{% else %}warning{% endif %} ms-2">
                                Billing: {{ latest_invoice.status|capfirst }}
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">No Invoice</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row">
                    <!-- Prescription Information -->
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Prescription Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Prescription ID:</div>
                            <div class="col-md-8">{{ prescription.id }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Date:</div>
                            <div class="col-md-8">{{ prescription.prescription_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Diagnosis:</div>
                            <div class="col-md-8">{{ prescription.diagnosis }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Status:</div>
                            <div class="col-md-8">
                                {% if prescription.status == 'pending' %}
                                    <span class="badge bg-warning">Pending</span>
                                {% elif prescription.status == 'processing' %}
                                    <span class="badge bg-info">Processing</span>
                                {% elif prescription.status == 'completed' %}
                                    <span class="badge bg-success">Completed</span>
                                {% elif prescription.status == 'cancelled' %}
                                    <span class="badge bg-danger">Cancelled</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Prescription Type:</div>
                            <div class="col-md-8">
                                {% if prescription.prescription_type == 'inpatient' %}
                                    <span class="badge bg-info">In-Patient (MAR/eMAR)</span>
                                {% else %}
                                    <span class="badge bg-secondary">Out-Patient (Take-Home)</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Notes:</div>
                            <div class="col-md-8">{{ prescription.notes|default:"No notes provided." }}</div>
                        </div>
                    </div>

                    <!-- Patient & Doctor Information -->
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Patient & Doctor Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Patient:</div>
                            <div class="col-md-8">
                                <a href="{% url 'patients:detail' prescription.patient.id %}">
                                    {{ prescription.patient.get_full_name }}
                                </a>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Patient ID:</div>
                            <div class="col-md-8">{{ prescription.patient.patient_id }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Patient Phone:</div>
                            <div class="col-md-8">{{ prescription.patient.phone_number }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Doctor:</div>
                            <div class="col-md-8">Dr. {{ prescription.doctor.get_full_name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Department:</div>
                            <div class="col-md-8">{{ prescription.doctor.profile.department|default:"Not specified" }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Specialization:</div>
                            <div class="col-md-8">{{ prescription.doctor.profile.specialization|default:"Not specified" }}</div>
                        </div>
                    </div>
                </div>

                <!-- Prescription Items -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="border-bottom pb-2 mb-0">Prescribed Medications</h5>
                            {% if prescription.status != 'completed' and prescription.status != 'cancelled' %}
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMedicationModal">
                                    <i class="fas fa-plus"></i> Add Medication
                                </button>
                            {% endif %}
                        </div>

                        {% if prescription_items %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Medication</th>
                                            <th>Dosage</th>
                                            <th>Frequency</th>
                                            <th>Duration</th>
                                            <th>Quantity</th>
                                            <th>Instructions</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in prescription_items %}
                                            <tr>
                                                <td>
                                                    <a href="{% url 'pharmacy:medication_detail' item.medication.id %}">
                                                        {{ item.medication.name }}
                                                    </a>
                                                    <div class="small text-muted">{{ item.medication.strength }} - {{ item.medication.dosage_form }}</div>
                                                </td>
                                                <td>{{ item.dosage }}</td>
                                                <td>{{ item.frequency }}</td>
                                                <td>{{ item.duration }}</td>
                                                <td>{{ item.quantity }}</td>
                                                <td>{{ item.instructions }}</td>
                                                <td>
                                                    {% if item.is_dispensed %}
                                                        <span class="badge bg-success">Dispensed</span>
                                                        <div class="small text-muted">{{ item.dispensed_date|date:"M d, Y" }}</div>
                                                    {% else %}
                                                        <span class="badge bg-warning">Pending</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if not item.is_dispensed and prescription.status != 'completed' and prescription.status != 'cancelled' %}
                                                        <a href="{% url 'pharmacy:delete_prescription_item' item.id %}" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No medications have been added to this prescription yet.
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- MAR/eMAR or Out-Patient Display -->
                {% if prescription.prescription_type == 'inpatient' %}
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Medication Administration Record (MAR/eMAR)</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Medication</th>
                                        <th>Dosage</th>
                                        <th>Frequency</th>
                                        <th>Duration</th>
                                        <th>Quantity</th>
                                        <th>Instructions</th>
                                        <th>Admin Times</th>
                                        <th>Administered By</th>
                                        <th>Date/Time</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in prescription_items %}
                                    <tr>
                                        <td>{{ item.medication.name }}</td>
                                        <td>{{ item.dosage }}</td>
                                        <td>{{ item.frequency }}</td>
                                        <td>{{ item.duration }}</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ item.instructions }}</td>
                                        <td>
                                            <!-- Example: 08:00, 14:00, 20:00 (could be improved with real schedule logic) -->
                                            <span class="badge bg-light text-dark">08:00</span>
                                            <span class="badge bg-light text-dark">14:00</span>
                                            <span class="badge bg-light text-dark">20:00</span>
                                        </td>
                                        <td>
                                            {% for log in item.dispensing_logs.all %}
                                                {{ log.dispensed_by.get_full_name }}<br>
                                            {% empty %}
                                                <span class="text-muted">-</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% for log in item.dispensing_logs.all %}
                                                {{ log.dispensed_date|date:'M d, Y H:i' }}<br>
                                            {% empty %}
                                                <span class="text-muted">-</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% if item.is_dispensed %}
                                                <span class="badge bg-success">Dispensed</span>
                                            {% else %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Out-Patient Prescription (Take-Home)</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This prescription is intended for the patient to take to a community pharmacy or for e-prescribing.
                        </div>
                        <ul>
                            <li><strong>Patient Instructions:</strong> Written in lay terms (e.g., "Take one tablet by mouth twice daily for 10 days.")</li>
                            <li><strong>Quantity/Refills:</strong> {{ prescription_items|length }} medications, see details above.</li>
                            <li><strong>Special Precautions:</strong> See instructions for each medication.</li>
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Status Update -->
                {% if prescription.status != 'completed' and prescription.status != 'cancelled' %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Update Status</h5>
                                </div>
                                <div class="card-body">
                                    {% if messages %}
                                        {% for message in messages %}
                                            {% if message.tags == 'error' and message.message == 'Invalid status.' %}
                                                <div class="alert alert-danger">{{ message }}</div>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                    <form method="post" action="{% url 'pharmacy:update_prescription_status' prescription.id %}" class="d-flex align-items-center">
                                        {% csrf_token %}
                                        <div class="me-3">
                                            <select name="status" class="form-select">
                                                <option value="pending" {% if prescription.status == 'pending' %}selected{% endif %}>Pending</option>
                                                <option value="approved" {% if prescription.status == 'approved' %}selected{% endif %}>Approved</option>
                                                <option value="dispensed" {% if prescription.status == 'dispensed' %}selected{% endif %}>Dispensed</option>
                                                <option value="partially_dispensed" {% if prescription.status == 'partially_dispensed' %}selected{% endif %}>Partially Dispensed</option>
                                                <option value="on_hold" {% if prescription.status == 'on_hold' %}selected{% endif %}>On Hold</option>
                                                <option value="cancelled" {% if prescription.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Update Status
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Prescriptions
                </a>
            </div>
        </div>
    </div>
</div>

<div class="card mb-3">
    <div class="card-header">
        <h5>Prescription Details</h5>
    </div>
    <div class="card-body">
        <p><strong>Patient:</strong> {{ prescription.patient.get_full_name }}</p>
        <p><strong>Doctor:</strong> {{ prescription.doctor.get_full_name }}</p>
        <p><strong>Date:</strong> {{ prescription.prescription_date }}</p>
        <p><strong>Status:</strong> {{ prescription.get_status_display }}</p>
        <p><strong>Payment Status:</strong> {{ prescription.get_payment_status_display }}</p>
        {% with invoice=prescription.invoices.all|first %}
            {% if invoice %}
                <p><strong>Invoice:</strong> <a href="{% url 'billing:detail' invoice.id %}">#{{ invoice.invoice_number }}</a> ({{ invoice.get_status_display }})</p>
            {% else %}
                <p><strong>Invoice:</strong> <span class="text-muted">No invoice generated</span></p>
            {% endif %}
        {% endwith %}
    </div>
</div>


{% if messages %}
    <div class="mt-3">
        {% for message in messages %}
            {% if message.tags != 'error' or message.message != 'Invalid status.' %}
                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endif %}
        {% endfor %}
    </div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for medication dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: $('#addMedicationModal')
        });
    });
</script>

<!-- Add Medication Modal -->
<div class="modal fade" id="addMedicationModal" tabindex="-1" aria-labelledby="addMedicationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addMedicationModalLabel">Add Medication</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'pharmacy:add_prescription_item' prescription.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ item_form.medication.id_for_label }}" class="form-label">Medication</label>
                            {{ item_form.medication|add_class:"form-select select2" }}
                            {% if item_form.medication.errors %}
                                <div class="text-danger">
                                    {{ item_form.medication.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.dosage.id_for_label }}" class="form-label">Dosage</label>
                            {{ item_form.dosage|add_class:"form-control" }}
                            {% if item_form.dosage.errors %}
                                <div class="text-danger">
                                    {{ item_form.dosage.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.frequency.id_for_label }}" class="form-label">Frequency</label>
                            {{ item_form.frequency|add_class:"form-control" }}
                            {% if item_form.frequency.errors %}
                                <div class="text-danger">
                                    {{ item_form.frequency.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.duration.id_for_label }}" class="form-label">Duration</label>
                            {{ item_form.duration|add_class:"form-control" }}
                            {% if item_form.duration.errors %}
                                <div class="text-danger">
                                    {{ item_form.duration.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.quantity.id_for_label }}" class="form-label">Quantity</label>
                            {{ item_form.quantity|add_class:"form-control" }}
                            {% if item_form.quantity.errors %}
                                <div class="text-danger">
                                    {{ item_form.quantity.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-8 mb-3">
                            <label for="{{ item_form.instructions.id_for_label }}" class="form-label">Instructions</label>
                            {{ item_form.instructions|add_class:"form-control" }}
                            {% if item_form.instructions.errors %}
                                <div class="text-danger">
                                    {{ item_form.instructions.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Medication</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}


