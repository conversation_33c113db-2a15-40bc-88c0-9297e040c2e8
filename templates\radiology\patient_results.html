{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Radiology Results for {{ patient.get_full_name }}</h2>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <strong>Patient Information</strong>
        </div>
        <div class="card-body">
            <p><strong>Patient Number:</strong> {{ patient.patient_number }}</p>
            <p><strong>Date of Birth:</strong> {{ patient.date_of_birth }}</p>
            <p><strong>Gender:</strong> {{ patient.get_gender_display }}</p>
        </div>
    </div>
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <strong>Radiology Results</strong>
        </div>
        <div class="card-body p-0">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Test</th>
                        <th>Findings</th>
                        <th>Impression</th>
                        <th>Performed By</th>
                        <th>Image</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in results %}
                    <tr>
                        <td>{{ result.result_date|date:'Y-m-d H:i' }}</td>
                        <td>{{ result.order.test.name }}</td>
                        <td>{{ result.findings|truncatewords:15 }}</td>
                        <td>{{ result.impression|truncatewords:10 }}</td>
                        <td>{{ result.performed_by.get_full_name|default:result.performed_by.username }}</td>
                        <td>
                            {% if result.image_file %}
                                <a href="{{ result.image_file.url }}" target="_blank">View</a>
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="6">No radiology results found for this patient.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
