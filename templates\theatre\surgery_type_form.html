{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
{% if form.instance.pk %}
Edit Surgery Type
{% else %}
Create Surgery Type
{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{% if form.instance.pk %}Edit Surgery Type{% else %}Create Surgery Type{% endif %}</h2>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <button type="submit" class="btn btn-primary">Save</button>
                <a href="{% url 'theatre:surgery_type_list' %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div
{% endblock %}