Metadata-Version: 2.4
Name: django-bootstrap5
Version: 25.1
Summary: Bootstrap 5 for Django
Project-URL: Changelog, https://github.com/zostera/django-bootstrap5/blob/main/CHANGELOG.md
Project-URL: Documentation, https://django-bootstrap5.readthedocs.io/
Project-URL: Homepage, https://github.com/zostera/django-bootstrap5
Project-URL: Issues, https://github.com/zostera/django-bootstrap5/issues
Project-URL: Source, https://github.com/zostera/django-bootstrap5
Author-email: <PERSON> <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) Zostera B.V. and individual contributors
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        * Redistributions of source code must retain the above copyright notice, this
          list of conditions and the following disclaimer.
        
        * Redistributions in binary form must reproduce the above copyright notice,
          this list of conditions and the following disclaimer in the documentation
          and/or other materials provided with the distribution.
        
        * Neither the name of the copyright holder nor the names of its
          contributors may be used to endorse or promote products derived from
          this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: AUTHORS
License-File: LICENSE
Keywords: bootstrap,bootstrap5,django
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 4.2
Classifier: Framework :: Django :: 5.0
Classifier: Framework :: Django :: 5.1
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Utilities
Requires-Python: >=3.9
Requires-Dist: django>=4.2
Provides-Extra: jinja
Requires-Dist: jinja2<4,>=3.0; extra == 'jinja'
Description-Content-Type: text/markdown

# django-bootstrap5

[![Tests](https://github.com/zostera/django-bootstrap5/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/zostera/django-bootstrap5/actions?workflow=test)
[![Coverage Status](https://coveralls.io/repos/github/zostera/django-bootstrap5/badge.svg?branch=main)](https://coveralls.io/github/zostera/django-bootstrap5?branch=main)
[![Latest PyPI version](https://img.shields.io/pypi/v/django-bootstrap5.svg)](https://pypi.python.org/pypi/django-bootstrap5)

Bootstrap 5 for Django.

## Goal

The goal of this project is to seamlessly blend Django and Bootstrap 5.

## Status

Ready for production. Issues and pull requests welcome, see [CONTRIBUTING.md](CONTRIBUTING.md).

## Requirements

This package requires a combination of Python and Django that is currently supported.

See "Supported Versions" on https://www.djangoproject.com/download/.

This package uses [uv](https://github.com/astral-sh/uv) and [just](https://github.com/casey/just) for local development.

## Documentation

The full documentation is at https://django-bootstrap5.readthedocs.io/

## Installation

1. Install using pip:

    ```console
    pip install django-bootstrap5
    ```

2. Add to `INSTALLED_APPS` in your `settings.py`:

   ```python
   INSTALLED_APPS = (
       # ...
       "django_bootstrap5",
       # ...
   )
   ```

3. In your templates, load the `django_bootstrap5` library and use the `bootstrap_*` tags. See example below.

## Example template

```jinja2
{% load django_bootstrap5 %}

<form action="/url/to/submit/" method="post" class="form">
    {% csrf_token %}

    {% bootstrap_form form %}

    {% bootstrap_button button_type="submit" content="OK" %}
    {% bootstrap_button button_type="reset" content="Cancel" %}
</form>
```

## Example app

An example app is provided in the folder `example`. You can run the example app with this command:

```console
just example
```

## Bugs and suggestions

If you have found a bug or if you have a request for additional functionality, please use the issue tracker on GitHub.

https://github.com/zostera/django-bootstrap5/issues

## License

You can use this under BSD-3-Clause. See [LICENSE](LICENSE) file for details.

## Author

Developed and maintained by [Zostera](https://zostera.nl).

Original author: [Dylan Verheul](https://github.com/dyve).

Thanks to everybody that has contributed pull requests, ideas, issues, comments and kind words.

Please see [AUTHORS](AUTHORS) for a list of contributors.
